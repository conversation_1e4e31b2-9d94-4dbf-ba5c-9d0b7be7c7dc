/*
 * 姿态数据显示逻辑实现文件
 * Attitude Data Display Logic Implementation
 */

#include "attitude_display.h"
#include <stdio.h>
#include <string.h>

/*=============================================================================
 * 私有变量 Private Variables
 *============================================================================*/
static display_config_t display_config = {
    .mode = DISPLAY_MODE_NORMAL,
    .show_title = true,
    .show_units = true,
    .auto_refresh = true,
    .refresh_interval = DISPLAY_UPDATE_INTERVAL,
    .decimal_places = DISPLAY_DECIMAL_PLACES
};

static volatile display_status_t display_status = DISPLAY_STATUS_OK;
static volatile uint32_t last_update_time = 0;
static volatile bool display_initialized = false;
static jy901s_attitude_t last_attitude = {0};

/*=============================================================================
 * 私有函数声明 Private Function Declarations
 *============================================================================*/
static void display_draw_title(void);
static void display_draw_attitude_data(const jy901s_attitude_t *attitude);
static void display_draw_status_bar(bool connected);
static bool display_data_changed(const jy901s_attitude_t *new_data);
static void display_draw_chinese_label(uint8_t x, uint8_t y, const char *label);

/*=============================================================================
 * 公共函数实现 Public Function Implementation
 *============================================================================*/

display_status_t attitude_display_init(void) {
    // 初始化OLED显示器 Initialize OLED display
    if (ssd1306_init() != SSD1306_STATUS_OK) {
        display_status = DISPLAY_STATUS_ERROR;
        return DISPLAY_STATUS_ERROR;
    }
    
    // 清空显示 Clear display
    ssd1306_clear();
    
    // 显示初始界面 Display initial interface
    display_draw_title();
    attitude_display_show_status(false); // 显示未连接状态
    
    // 更新显示 Update display
    if (ssd1306_update() != SSD1306_STATUS_OK) {
        display_status = DISPLAY_STATUS_ERROR;
        return DISPLAY_STATUS_ERROR;
    }
    
    display_initialized = true;
    display_status = DISPLAY_STATUS_OK;
    last_update_time = DL_Common_getTIMERCount(SYSTEM_TIMER_INSTANCE);
    
    return DISPLAY_STATUS_OK;
}

display_status_t attitude_display_update(void) {
    if (!display_initialized) {
        return DISPLAY_STATUS_ERROR;
    }
    
    // 获取当前姿态数据 Get current attitude data
    jy901s_attitude_t current_attitude;
    jy901s_status_t sensor_status = jy901s_get_attitude(&current_attitude);
    
    // 检查传感器状态 Check sensor status
    if (sensor_status != JY901S_STATUS_OK) {
        display_status = DISPLAY_STATUS_SENSOR_ERROR;
        attitude_display_show_error((uint8_t)sensor_status);
        return DISPLAY_STATUS_SENSOR_ERROR;
    }
    
    // 检查数据有效性 Check data validity
    if (!current_attitude.valid) {
        display_status = DISPLAY_STATUS_NO_DATA;
        attitude_display_show_status(false);
        return DISPLAY_STATUS_NO_DATA;
    }
    
    // 检查是否需要更新 Check if update is needed
    if (!display_data_changed(&current_attitude) && !attitude_display_need_update()) {
        return DISPLAY_STATUS_OK;
    }
    
    // 清空显示缓冲区 Clear display buffer
    ssd1306_clear();
    
    // 绘制界面 Draw interface
    if (display_config.show_title) {
        display_draw_title();
    }
    
    display_draw_attitude_data(&current_attitude);
    display_draw_status_bar(true);
    
    // 更新显示 Update display
    if (ssd1306_update() != SSD1306_STATUS_OK) {
        display_status = DISPLAY_STATUS_ERROR;
        return DISPLAY_STATUS_ERROR;
    }
    
    // 保存当前数据 Save current data
    last_attitude = current_attitude;
    last_update_time = DL_Common_getTIMERCount(SYSTEM_TIMER_INSTANCE);
    display_status = DISPLAY_STATUS_OK;
    
    return DISPLAY_STATUS_OK;
}

display_status_t attitude_display_refresh(void) {
    if (!display_initialized) {
        return DISPLAY_STATUS_ERROR;
    }
    
    // 强制更新标志 Force update flag
    last_update_time = 0;
    
    return attitude_display_update();
}

display_status_t attitude_display_set_config(const display_config_t *config) {
    if (config == NULL) {
        return DISPLAY_STATUS_ERROR;
    }
    
    display_config = *config;
    return DISPLAY_STATUS_OK;
}

display_status_t attitude_display_get_config(display_config_t *config) {
    if (config == NULL) {
        return DISPLAY_STATUS_ERROR;
    }
    
    *config = display_config;
    return DISPLAY_STATUS_OK;
}

display_status_t attitude_display_set_mode(display_mode_t mode) {
    display_config.mode = mode;
    return attitude_display_refresh();
}

void attitude_display_show_status(bool connected) {
    ssd1306_clear();
    display_draw_title();
    
    if (connected) {
        ssd1306_draw_string(32, 32, "已连接", SSD1306_FONT_8X16, 1);
    } else {
        ssd1306_draw_string(32, 32, STR_CONNECTING, SSD1306_FONT_8X16, 1);
    }
    
    ssd1306_update();
}

void attitude_display_show_error(uint8_t error_code) {
    ssd1306_clear();
    display_draw_title();
    
    char error_msg[32];
    snprintf(error_msg, sizeof(error_msg), "%s: %d", STR_ERROR, error_code);
    ssd1306_draw_string(16, 32, error_msg, SSD1306_FONT_8X16, 1);
    
    ssd1306_update();
}

void attitude_display_clear(void) {
    ssd1306_clear();
    ssd1306_update();
}

bool attitude_display_need_update(void) {
    if (!display_config.auto_refresh) {
        return false;
    }
    
    uint32_t current_time = DL_Common_getTIMERCount(SYSTEM_TIMER_INSTANCE);
    return (current_time - last_update_time) >= display_config.refresh_interval;
}

display_status_t attitude_display_get_status(void) {
    return display_status;
}

/*=============================================================================
 * 私有函数实现 Private Function Implementation
 *============================================================================*/

static void display_draw_title(void) {
    // 绘制标题 Draw title
    ssd1306_draw_chinese(16, 0, UNICODE_姿, 1);
    ssd1306_draw_chinese(32, 0, UNICODE_态, 1);
    ssd1306_draw_chinese(48, 0, UNICODE_传, 1);
    ssd1306_draw_chinese(64, 0, UNICODE_感, 1);
    ssd1306_draw_chinese(80, 0, UNICODE_器, 1);
    
    // 绘制分隔线 Draw separator line
    ssd1306_draw_line(0, 15, SSD1306_WIDTH - 1, 15, 1);
}

static void display_draw_attitude_data(const jy901s_attitude_t *attitude) {
    if (attitude == NULL) return;
    
    char angle_str[16];
    
    // 绘制俯仰角 Draw pitch angle
    display_draw_chinese_label(0, DISPLAY_PITCH_Y, STR_PITCH);
    attitude_format_angle(attitude->pitch, angle_str, sizeof(angle_str), display_config.decimal_places);
    ssd1306_draw_string(DISPLAY_VALUE_X, DISPLAY_PITCH_Y, angle_str, SSD1306_FONT_8X16, 1);
    if (display_config.show_units) {
        ssd1306_draw_string(DISPLAY_VALUE_X + 48, DISPLAY_PITCH_Y, STR_DEGREE, SSD1306_FONT_8X16, 1);
    }
    
    // 绘制横滚角 Draw roll angle
    display_draw_chinese_label(0, DISPLAY_ROLL_Y, STR_ROLL);
    attitude_format_angle(attitude->roll, angle_str, sizeof(angle_str), display_config.decimal_places);
    ssd1306_draw_string(DISPLAY_VALUE_X, DISPLAY_ROLL_Y, angle_str, SSD1306_FONT_8X16, 1);
    if (display_config.show_units) {
        ssd1306_draw_string(DISPLAY_VALUE_X + 48, DISPLAY_ROLL_Y, STR_DEGREE, SSD1306_FONT_8X16, 1);
    }
    
    // 绘制偏航角 Draw yaw angle
    display_draw_chinese_label(0, DISPLAY_YAW_Y, STR_YAW);
    attitude_format_angle(attitude->yaw, angle_str, sizeof(angle_str), display_config.decimal_places);
    ssd1306_draw_string(DISPLAY_VALUE_X, DISPLAY_YAW_Y, angle_str, SSD1306_FONT_8X16, 1);
    if (display_config.show_units) {
        ssd1306_draw_string(DISPLAY_VALUE_X + 48, DISPLAY_YAW_Y, STR_DEGREE, SSD1306_FONT_8X16, 1);
    }
}

static void display_draw_status_bar(bool connected) {
    // 绘制底部状态栏 Draw bottom status bar
    if (connected) {
        ssd1306_set_pixel(SSD1306_WIDTH - 8, SSD1306_HEIGHT - 8, 1); // 连接指示点
    }
    
    // 显示更新时间指示 Show update time indicator
    uint8_t indicator_x = SSD1306_WIDTH - 16;
    ssd1306_set_pixel(indicator_x, SSD1306_HEIGHT - 4, 1);
    ssd1306_set_pixel(indicator_x + 1, SSD1306_HEIGHT - 4, 1);
}

static bool display_data_changed(const jy901s_attitude_t *new_data) {
    if (new_data == NULL) return false;
    
    // 检查数据是否发生变化 Check if data has changed
    float pitch_diff = new_data->pitch - last_attitude.pitch;
    float roll_diff = new_data->roll - last_attitude.roll;
    float yaw_diff = new_data->yaw - last_attitude.yaw;
    
    // 设置变化阈值 Set change threshold
    const float threshold = 0.1f; // 0.1度变化阈值
    
    return (pitch_diff > threshold || pitch_diff < -threshold ||
            roll_diff > threshold || roll_diff < -threshold ||
            yaw_diff > threshold || yaw_diff < -threshold);
}

static void display_draw_chinese_label(uint8_t x, uint8_t y, const char *label) {
    // 简化的中文标签绘制 Simplified Chinese label drawing
    if (strcmp(label, STR_PITCH) == 0) {
        ssd1306_draw_chinese(x, y, UNICODE_俯, 1);
        ssd1306_draw_chinese(x + 16, y, UNICODE_仰, 1);
        ssd1306_draw_chinese(x + 32, y, UNICODE_角, 1);
        ssd1306_draw_string(x + 48, y, ":", SSD1306_FONT_8X16, 1);
    } else if (strcmp(label, STR_ROLL) == 0) {
        ssd1306_draw_chinese(x, y, UNICODE_横, 1);
        ssd1306_draw_chinese(x + 16, y, UNICODE_滚, 1);
        ssd1306_draw_chinese(x + 32, y, UNICODE_角, 1);
        ssd1306_draw_string(x + 48, y, ":", SSD1306_FONT_8X16, 1);
    } else if (strcmp(label, STR_YAW) == 0) {
        ssd1306_draw_chinese(x, y, UNICODE_偏, 1);
        ssd1306_draw_chinese(x + 16, y, UNICODE_航, 1);
        ssd1306_draw_chinese(x + 32, y, UNICODE_角, 1);
        ssd1306_draw_string(x + 48, y, ":", SSD1306_FONT_8X16, 1);
    }
}

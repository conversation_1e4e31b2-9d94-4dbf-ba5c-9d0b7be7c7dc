/*
 * 姿态数据显示逻辑头文件
 * Attitude Data Display Logic Header File
 *
 * 功能：界面布局、数据格式化、实时更新显示
 * Features: Interface layout, data formatting, real-time display update
 */

#ifndef ATTITUDE_DISPLAY_H
#define ATTITUDE_DISPLAY_H

#include <stdint.h>
#include <stdbool.h>
#include <stdio.h>

// 基本配置常量 Basic configuration constants
#define DISPLAY_DECIMAL_PLACES      1           // 小数位数
#define DISPLAY_UPDATE_INTERVAL     100         // 更新间隔(ms)

// 中文字符串定义 Chinese String Definitions
#define STR_TITLE                   "姿态传感器"     // 标题
#define STR_PITCH                   "俯仰角:"        // 俯仰角标签
#define STR_ROLL                    "横滚角:"        // 横滚角标签
#define STR_YAW                     "偏航角:"        // 偏航角标签
#define STR_DEGREE                  "°"             // 度数符号
#define STR_CONNECTING              "连接中..."       // 连接状态
#define STR_ERROR                   "错误"          // 错误状态

#ifdef __cplusplus
extern "C" {
#endif

/*=============================================================================
 * 显示状态枚举 Display Status Enumeration
 *============================================================================*/
typedef enum {
    DISPLAY_STATUS_OK = 0,          // 正常 Normal
    DISPLAY_STATUS_ERROR,           // 错误 Error
    DISPLAY_STATUS_NO_DATA,         // 无数据 No data
    DISPLAY_STATUS_SENSOR_ERROR     // 传感器错误 Sensor error
} display_status_t;

/*=============================================================================
 * 显示模式枚举 Display Mode Enumeration
 *============================================================================*/
typedef enum {
    DISPLAY_MODE_NORMAL = 0,        // 正常模式 Normal mode
    DISPLAY_MODE_DETAILED,          // 详细模式 Detailed mode
    DISPLAY_MODE_SIMPLE             // 简洁模式 Simple mode
} display_mode_t;

/*=============================================================================
 * 显示配置结构 Display Configuration Structure
 *============================================================================*/
typedef struct {
    display_mode_t mode;            // 显示模式 Display mode
    bool show_title;                // 显示标题 Show title
    bool show_units;                // 显示单位 Show units
    bool auto_refresh;              // 自动刷新 Auto refresh
    uint16_t refresh_interval;      // 刷新间隔(ms) Refresh interval
    uint8_t decimal_places;         // 小数位数 Decimal places
} display_config_t;

/*=============================================================================
 * 函数声明 Function Declarations
 *============================================================================*/

/**
 * @brief 初始化姿态显示系统
 * @brief Initialize attitude display system
 * @return display_status_t 初始化状态
 */
display_status_t attitude_display_init(void);

/**
 * @brief 更新姿态数据显示
 * @brief Update attitude data display
 * @return display_status_t 更新状态
 */
display_status_t attitude_display_update(void);

/**
 * @brief 强制刷新显示
 * @brief Force refresh display
 * @return display_status_t 刷新状态
 */
display_status_t attitude_display_refresh(void);

/**
 * @brief 设置显示配置
 * @brief Set display configuration
 * @param config 配置指针 Configuration pointer
 * @return display_status_t 设置状态
 */
display_status_t attitude_display_set_config(const display_config_t *config);

/**
 * @brief 获取显示配置
 * @brief Get display configuration
 * @param config 配置指针 Configuration pointer
 * @return display_status_t 获取状态
 */
display_status_t attitude_display_get_config(display_config_t *config);

/**
 * @brief 设置显示模式
 * @brief Set display mode
 * @param mode 显示模式 Display mode
 * @return display_status_t 设置状态
 */
display_status_t attitude_display_set_mode(display_mode_t mode);

/**
 * @brief 显示连接状态
 * @brief Display connection status
 * @param connected 连接状态 Connection status
 */
void attitude_display_show_status(bool connected);

/**
 * @brief 显示错误信息
 * @brief Display error message
 * @param error_code 错误代码 Error code
 */
void attitude_display_show_error(uint8_t error_code);

/**
 * @brief 清空显示
 * @brief Clear display
 */
void attitude_display_clear(void);

/**
 * @brief 检查是否需要更新显示
 * @brief Check if display update is needed
 * @return bool 是否需要更新 Whether update is needed
 */
bool attitude_display_need_update(void);

/**
 * @brief 获取显示状态
 * @brief Get display status
 * @return display_status_t 显示状态
 */
display_status_t attitude_display_get_status(void);

/*=============================================================================
 * 内联函数 Inline Functions
 *============================================================================*/

/**
 * @brief 格式化角度值为字符串
 * @brief Format angle value to string
 * @param angle 角度值 Angle value
 * @param buffer 缓冲区 Buffer
 * @param buffer_size 缓冲区大小 Buffer size
 * @param decimal_places 小数位数 Decimal places
 */
static inline void attitude_format_angle(float angle, char *buffer, uint8_t buffer_size, uint8_t decimal_places) {
    if (buffer == NULL) return;
    
    // 限制角度范围 Limit angle range
    if (angle > 180.0f) angle = 180.0f;
    if (angle < -180.0f) angle = -180.0f;
    
    // 格式化字符串 Format string
    snprintf(buffer, buffer_size, "%.*f", decimal_places, angle);
}

/**
 * @brief 检查角度值是否有效
 * @brief Check if angle value is valid
 * @param angle 角度值 Angle value
 * @return bool 是否有效 Whether valid
 */
static inline bool attitude_is_angle_valid(float angle) {
    return (angle >= -180.0f && angle <= 180.0f);
}

#ifdef __cplusplus
}
#endif

#endif /* ATTITUDE_DISPLAY_H */

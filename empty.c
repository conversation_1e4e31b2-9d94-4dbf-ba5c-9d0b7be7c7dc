/*
 * MSPM0G3507 JY901S姿态传感器显示系统主程序
 * MSPM0G3507 JY901S Attitude Sensor Display System Main Program
 *
 * 功能：集成JY901S传感器驱动和OLED显示，实时显示姿态数据
 * Features: Integrate JY901S sensor driver and OLED display, real-time attitude data display
 *
 * Copyright (c) 2024, MSPM0G3507 JY901S Attitude Display System
 * All rights reserved.
 */

#include "ti_msp_dl_config.h"
#include "config.h"
#include "drivers/jy901s.h"
#include "drivers/ssd1306.h"
#include "app/attitude_display.h"

/*=============================================================================
 * 全局变量 Global Variables
 *============================================================================*/
static volatile bool system_initialized = false;   // 系统初始化标志
static volatile uint32_t system_tick = 0;          // 系统滴答计数
static volatile bool led_state = false;            // LED状态

/*=============================================================================
 * 函数声明 Function Declarations
 *============================================================================*/
static void system_init(void);                     // 系统初始化
static void system_error_handler(uint8_t error);   // 系统错误处理
static void status_led_toggle(void);               // 状态LED切换
static void system_watchdog_feed(void);            // 喂看门狗

/*=============================================================================
 * 主函数 Main Function
 *============================================================================*/
int main(void) {
    // 硬件初始化 Hardware initialization
    SYSCFG_DL_init();

    // 系统初始化 System initialization
    system_init();

    // 主循环 Main loop
    while (1) {
        // 更新姿态显示 Update attitude display
        display_status_t display_status = attitude_display_update();

        // 处理显示错误 Handle display errors
        if (display_status != DISPLAY_STATUS_OK) {
            if (display_status == DISPLAY_STATUS_SENSOR_ERROR) {
                status_led_toggle(); // 快速闪烁表示传感器错误
                DL_Common_delayCycles(SYSTEM_CLOCK_FREQ / 20); // 50ms延时
            } else if (display_status == DISPLAY_STATUS_NO_DATA) {
                // 慢速闪烁表示无数据
                if ((system_tick % 10) == 0) {
                    status_led_toggle();
                }
            }
        } else {
            // 正常工作时LED常亮 LED on during normal operation
            if (!led_state) {
                DL_GPIO_setPins(STATUS_LED_PORT, STATUS_LED_PIN);
                led_state = true;
            }
        }

        // 系统定时任务 System timing tasks
        system_tick++;

        // 喂看门狗 Feed watchdog
        if ((system_tick % 100) == 0) {
            system_watchdog_feed();
        }

        // 短暂延时 Brief delay
        DL_Common_delayCycles(SYSTEM_CLOCK_FREQ / 100); // 10ms延时
    }
}

/*=============================================================================
 * 中断处理函数 Interrupt Handlers
 *============================================================================*/

// UART0中断处理函数 UART0 interrupt handler
void UART0_IRQHandler(void) {
    jy901s_uart_irq_handler();
}

// 定时器中断处理函数 Timer interrupt handler
void TIMG0_IRQHandler(void) {
    switch (DL_TimerG_getPendingInterrupt(SYSTEM_TIMER_INSTANCE)) {
        case DL_TIMER_IIDX_ZERO:
            // 定时器溢出中断 Timer overflow interrupt
            system_tick++;
            break;
        default:
            break;
    }
}

/*=============================================================================
 * 私有函数实现 Private Function Implementation
 *============================================================================*/

static void system_init(void) {
    // 初始化JY901S传感器 Initialize JY901S sensor
    if (jy901s_init() != JY901S_STATUS_OK) {
        system_error_handler(1);
        return;
    }

    // 初始化姿态显示系统 Initialize attitude display system
    if (attitude_display_init() != DISPLAY_STATUS_OK) {
        system_error_handler(2);
        return;
    }

    // 配置状态LED Configure status LED
    DL_GPIO_initDigitalOutput(STATUS_LED_PIN);
    DL_GPIO_setPins(STATUS_LED_PORT, STATUS_LED_PIN);
    led_state = true;

    // 启用全局中断 Enable global interrupts
    DL_SYSCTL_enableSleepOnExit();
    NVIC_EnableIRQ(UART0_INT_IRQN);
    NVIC_EnableIRQ(TIMG0_INT_IRQN);

    system_initialized = true;
}

static void system_error_handler(uint8_t error) {
    // 错误处理：快速闪烁LED Error handling: fast LED blinking
    while (1) {
        status_led_toggle();
        DL_Common_delayCycles(SYSTEM_CLOCK_FREQ / 10); // 100ms延时
        status_led_toggle();
        DL_Common_delayCycles(SYSTEM_CLOCK_FREQ / 10); // 100ms延时

        // 显示错误代码 Display error code
        attitude_display_show_error(error);

        // 延时后重试 Delay and retry
        DL_Common_delayCycles(SYSTEM_CLOCK_FREQ * 2); // 2秒延时
    }
}

static void status_led_toggle(void) {
    if (led_state) {
        DL_GPIO_clearPins(STATUS_LED_PORT, STATUS_LED_PIN);
        led_state = false;
    } else {
        DL_GPIO_setPins(STATUS_LED_PORT, STATUS_LED_PIN);
        led_state = true;
    }
}

static void system_watchdog_feed(void) {
    // 简单的看门狗喂狗实现 Simple watchdog feeding implementation
    // 在实际应用中可以添加更复杂的系统健康检查
    // In real applications, more complex system health checks can be added
    __NOP(); // 空操作，表示系统正常运行
}

/*
 * Copyright (c) 2024, MSPM0G3507 JY901S Attitude Display System
 * All rights reserved.
 *
 * 统一配置文件 - 管理所有硬件和软件参数
 * Unified Configuration File - Manage all hardware and software parameters
 */

#ifndef CONFIG_H
#define CONFIG_H

#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/*=============================================================================
 * 系统配置 System Configuration
 *============================================================================*/
#define SYSTEM_CLOCK_FREQ           32000000    // 系统时钟频率 32MHz
#define SYSTEM_VERSION              "1.0.0"     // 系统版本号

/*=============================================================================
 * JY901S传感器配置 JY901S Sensor Configuration  
 *============================================================================*/
#define JY901S_UART_INSTANCE        UART0       // UART实例
#define JY901S_UART_BAUDRATE        9600        // 波特率
#define JY901S_UART_DATA_BITS       8           // 数据位
#define JY901S_UART_STOP_BITS       1           // 停止位
#define JY901S_UART_PARITY          0           // 奇偶校验(0=无)

// JY901S引脚定义 Pin Definitions
#define JY901S_UART_TX_PORT         GPIOA       // TX引脚端口
#define JY901S_UART_TX_PIN          DL_GPIO_PIN_10  // TX引脚
#define JY901S_UART_RX_PORT         GPIOA       // RX引脚端口  
#define JY901S_UART_RX_PIN          DL_GPIO_PIN_11  // RX引脚

// JY901S数据包配置 Data Packet Configuration
#define JY901S_FRAME_HEADER         0x55        // 数据包头
#define JY901S_ANGLE_TYPE           0x53        // 角度数据类型
#define JY901S_PACKET_SIZE          11          // 数据包大小
#define JY901S_RX_BUFFER_SIZE       128         // 接收缓冲区大小
#define JY901S_ANGLE_SCALE          32768.0f    // 角度缩放因子
#define JY901S_ANGLE_RANGE          180.0f      // 角度范围

/*=============================================================================
 * SSD1306 OLED显示配置 SSD1306 OLED Display Configuration
 *============================================================================*/
#define SSD1306_I2C_INSTANCE       I2C0        // I2C实例
#define SSD1306_I2C_ADDRESS         0x3C        // I2C地址
#define SSD1306_I2C_CLOCK_FREQ      100000      // I2C时钟频率 100kHz

// SSD1306引脚定义 Pin Definitions
#define SSD1306_I2C_SCL_PORT        GPIOA       // SCL引脚端口
#define SSD1306_I2C_SCL_PIN         DL_GPIO_PIN_0   // SCL引脚
#define SSD1306_I2C_SDA_PORT        GPIOA       // SDA引脚端口
#define SSD1306_I2C_SDA_PIN         DL_GPIO_PIN_1   // SDA引脚

// SSD1306显示参数 Display Parameters
#define SSD1306_WIDTH               128         // 屏幕宽度
#define SSD1306_HEIGHT              64          // 屏幕高度
#define SSD1306_BUFFER_SIZE         ((SSD1306_WIDTH * SSD1306_HEIGHT) / 8)
#define SSD1306_CONTRAST            0x8F        // 对比度
#define SSD1306_REFRESH_RATE        10          // 刷新频率 10Hz

/*=============================================================================
 * 显示界面配置 Display Interface Configuration
 *============================================================================*/
#define DISPLAY_TITLE_Y             0           // 标题Y坐标
#define DISPLAY_PITCH_Y             16          // 俯仰角Y坐标
#define DISPLAY_ROLL_Y              32          // 横滚角Y坐标  
#define DISPLAY_YAW_Y               48          // 偏航角Y坐标
#define DISPLAY_VALUE_X             64          // 数值X坐标
#define DISPLAY_DECIMAL_PLACES      1           // 小数位数
#define DISPLAY_UPDATE_INTERVAL     100         // 更新间隔(ms)

/*=============================================================================
 * 字体配置 Font Configuration
 *============================================================================*/
#define FONT_ASCII_WIDTH            8           // ASCII字符宽度
#define FONT_ASCII_HEIGHT           16          // ASCII字符高度
#define FONT_CHINESE_WIDTH          16          // 中文字符宽度
#define FONT_CHINESE_HEIGHT         16          // 中文字符高度

/*=============================================================================
 * 系统状态配置 System Status Configuration
 *============================================================================*/
#define STATUS_LED_PORT             GPIOA       // 状态LED端口
#define STATUS_LED_PIN              DL_GPIO_PIN_26  // 状态LED引脚
#define STATUS_LED_BLINK_FAST       100         // 快闪间隔(ms)
#define STATUS_LED_BLINK_SLOW       500         // 慢闪间隔(ms)

/*=============================================================================
 * 调试配置 Debug Configuration
 *============================================================================*/
#ifdef DEBUG
#define DEBUG_UART_INSTANCE         UART1       // 调试UART实例
#define DEBUG_UART_BAUDRATE         115200      // 调试波特率
#define DEBUG_ENABLE                1           // 启用调试
#else
#define DEBUG_ENABLE                0           // 禁用调试
#endif

/*=============================================================================
 * 错误处理配置 Error Handling Configuration
 *============================================================================*/
#define ERROR_RETRY_COUNT           3           // 错误重试次数
#define ERROR_TIMEOUT_MS            1000        // 错误超时时间
#define WATCHDOG_TIMEOUT_MS         5000        // 看门狗超时时间

/*=============================================================================
 * 功耗管理配置 Power Management Configuration
 *============================================================================*/
#define POWER_SAVE_ENABLE           1           // 启用省电模式
#define POWER_SAVE_IDLE_TIME        5000        // 空闲时间(ms)
#define POWER_SAVE_DISPLAY_DIM      30          // 显示变暗对比度

/*=============================================================================
 * 中文字符串定义 Chinese String Definitions
 *============================================================================*/
#define STR_TITLE                   "姿态传感器"     // 标题
#define STR_PITCH                   "俯仰角:"        // 俯仰角标签
#define STR_ROLL                    "横滚角:"        // 横滚角标签
#define STR_YAW                     "偏航角:"        // 偏航角标签
#define STR_DEGREE                  "°"             // 度数符号
#define STR_CONNECTING              "连接中..."       // 连接状态
#define STR_ERROR                   "错误"          // 错误状态

/*=============================================================================
 * 编译时配置检查 Compile-time Configuration Check
 *============================================================================*/
#if JY901S_RX_BUFFER_SIZE < JY901S_PACKET_SIZE * 2
#error "JY901S接收缓冲区太小 JY901S RX buffer too small"
#endif

#if SSD1306_REFRESH_RATE > 50
#error "OLED刷新频率过高 OLED refresh rate too high"
#endif

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_H */

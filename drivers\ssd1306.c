/*
 * SSD1306 OLED显示驱动实现文件
 * SSD1306 OLED Display Driver Implementation
 */

#include "ti_msp_dl_config.h"
#include "config.h"
#include "ssd1306.h"
#include "font.h"
#include <stdio.h>

/*=============================================================================
 * 私有变量 Private Variables
 *============================================================================*/
uint8_t ssd1306_buffer[SSD1306_BUFFER_SIZE];   // 显示缓冲区
static bool display_initialized = false;        // 初始化标志

/*=============================================================================
 * 私有函数声明 Private Function Declarations
 *============================================================================*/
static ssd1306_status_t ssd1306_write_command(uint8_t cmd);
static ssd1306_status_t ssd1306_write_data(const uint8_t *data, uint16_t len);
static void ssd1306_abs(int16_t *x);

/*=============================================================================
 * 公共函数实现 Public Function Implementation
 *============================================================================*/

ssd1306_status_t ssd1306_init(void) {
    // 初始化序列 Initialization sequence
    const uint8_t init_commands[] = {
        SSD1306_CMD_DISPLAY_OFF,        // 关闭显示
        SSD1306_CMD_SET_CLOCK_DIV, 0x80, // 设置时钟分频
        0xA8, 0x3F,                     // 设置复用比 (64-1)
        0xD3, 0x00,                     // 设置显示偏移
        SSD1306_CMD_SET_START_LINE,     // 设置起始行
        SSD1306_CMD_CHARGE_PUMP, 0x14,  // 启用电荷泵
        SSD1306_CMD_SET_MEMORY_MODE, 0x00, // 水平地址模式
        SSD1306_CMD_SET_SEGMENT_REMAP,  // 段重映射
        SSD1306_CMD_SET_COM_SCAN_DEC,   // COM扫描方向
        SSD1306_CMD_SET_COM_PINS, 0x12, // COM引脚配置
        SSD1306_CMD_SET_CONTRAST, SSD1306_CONTRAST, // 设置对比度
        SSD1306_CMD_SET_PRECHARGE, 0xF1, // 预充电周期
        SSD1306_CMD_SET_VCOM_DETECT, 0x40, // VCOM检测电平
        0xA4,                           // 全局显示开启
        SSD1306_CMD_NORMAL_DISPLAY,     // 正常显示
        SSD1306_CMD_DISPLAY_ON          // 开启显示
    };
    
    // 发送初始化命令 Send initialization commands
    for (uint8_t i = 0; i < sizeof(init_commands); i++) {
        if (ssd1306_write_command(init_commands[i]) != SSD1306_STATUS_OK) {
            return SSD1306_STATUS_I2C_ERROR;
        }
    }
    
    // 清空缓冲区 Clear buffer
    ssd1306_clear();
    
    // 更新显示 Update display
    if (ssd1306_update() != SSD1306_STATUS_OK) {
        return SSD1306_STATUS_I2C_ERROR;
    }
    
    display_initialized = true;
    return SSD1306_STATUS_OK;
}

void ssd1306_clear(void) {
    memset(ssd1306_buffer, 0, SSD1306_BUFFER_SIZE);
}

ssd1306_status_t ssd1306_update(void) {
    if (!display_initialized) {
        return SSD1306_STATUS_ERROR;
    }
    
    // 设置列地址范围 Set column address range
    if (ssd1306_write_command(SSD1306_CMD_SET_COLUMN_ADDR) != SSD1306_STATUS_OK ||
        ssd1306_write_command(0) != SSD1306_STATUS_OK ||
        ssd1306_write_command(SSD1306_WIDTH - 1) != SSD1306_STATUS_OK) {
        return SSD1306_STATUS_I2C_ERROR;
    }
    
    // 设置页地址范围 Set page address range
    if (ssd1306_write_command(SSD1306_CMD_SET_PAGE_ADDR) != SSD1306_STATUS_OK ||
        ssd1306_write_command(0) != SSD1306_STATUS_OK ||
        ssd1306_write_command((SSD1306_HEIGHT / 8) - 1) != SSD1306_STATUS_OK) {
        return SSD1306_STATUS_I2C_ERROR;
    }
    
    // 发送显示数据 Send display data
    return ssd1306_write_data(ssd1306_buffer, SSD1306_BUFFER_SIZE);
}

void ssd1306_set_pixel(uint8_t x, uint8_t y, uint8_t color) {
    if (x >= SSD1306_WIDTH || y >= SSD1306_HEIGHT) return;
    
    uint16_t index = x + (y / 8) * SSD1306_WIDTH;
    uint8_t bit = y % 8;
    
    if (color) {
        ssd1306_buffer[index] |= (1 << bit);
    } else {
        ssd1306_buffer[index] &= ~(1 << bit);
    }
}

void ssd1306_draw_line(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint8_t color) {
    int16_t dx = x1 - x0;
    int16_t dy = y1 - y0;
    
    ssd1306_abs(&dx);
    ssd1306_abs(&dy);
    
    int16_t sx = (x0 < x1) ? 1 : -1;
    int16_t sy = (y0 < y1) ? 1 : -1;
    int16_t err = dx - dy;
    
    while (true) {
        ssd1306_set_pixel(x0, y0, color);
        
        if (x0 == x1 && y0 == y1) break;
        
        int16_t e2 = 2 * err;
        if (e2 > -dy) {
            err -= dy;
            x0 += sx;
        }
        if (e2 < dx) {
            err += dx;
            y0 += sy;
        }
    }
}

void ssd1306_draw_rect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, uint8_t color) {
    ssd1306_draw_line(x, y, x + width - 1, y, color);                    // 上边
    ssd1306_draw_line(x, y + height - 1, x + width - 1, y + height - 1, color); // 下边
    ssd1306_draw_line(x, y, x, y + height - 1, color);                   // 左边
    ssd1306_draw_line(x + width - 1, y, x + width - 1, y + height - 1, color);  // 右边
}

void ssd1306_draw_char(uint8_t x, uint8_t y, char ch, ssd1306_font_t font, uint8_t color) {
    if (font == SSD1306_FONT_8X16) {
        const uint8_t *char_data = font_get_ascii_data(ch);
        if (char_data == NULL) return;
        
        for (uint8_t i = 0; i < 16; i++) {
            uint8_t line = char_data[i];
            for (uint8_t j = 0; j < 8; j++) {
                if (line & (0x80 >> j)) {
                    ssd1306_set_pixel(x + j, y + i, color);
                }
            }
        }
    }
}

void ssd1306_draw_string(uint8_t x, uint8_t y, const char *str, ssd1306_font_t font, uint8_t color) {
    uint8_t char_width = (font == SSD1306_FONT_8X16) ? FONT_ASCII_WIDTH : FONT_CHINESE_WIDTH;
    
    while (*str) {
        ssd1306_draw_char(x, y, *str, font, color);
        x += char_width;
        str++;
        
        if (x >= SSD1306_WIDTH) break;
    }
}

void ssd1306_draw_chinese(uint8_t x, uint8_t y, uint16_t unicode, uint8_t color) {
    const uint8_t *char_data = font_get_chinese_data(unicode);
    if (char_data == NULL) return;
    
    for (uint8_t i = 0; i < 16; i++) {
        uint16_t line = (char_data[i * 2] << 8) | char_data[i * 2 + 1];
        for (uint8_t j = 0; j < 16; j++) {
            if (line & (0x8000 >> j)) {
                ssd1306_set_pixel(x + j, y + i, color);
            }
        }
    }
}

void ssd1306_draw_number(uint8_t x, uint8_t y, float num, uint8_t decimal_places, 
                        ssd1306_font_t font, uint8_t color) {
    char buffer[16];
    snprintf(buffer, sizeof(buffer), "%.*f", decimal_places, num);
    ssd1306_draw_string(x, y, buffer, font, color);
}

ssd1306_status_t ssd1306_set_contrast(uint8_t contrast) {
    if (ssd1306_write_command(SSD1306_CMD_SET_CONTRAST) != SSD1306_STATUS_OK ||
        ssd1306_write_command(contrast) != SSD1306_STATUS_OK) {
        return SSD1306_STATUS_I2C_ERROR;
    }
    return SSD1306_STATUS_OK;
}

ssd1306_status_t ssd1306_set_mode(ssd1306_mode_t mode) {
    uint8_t cmd = (mode == SSD1306_MODE_INVERSE) ? SSD1306_CMD_INVERSE_DISPLAY : SSD1306_CMD_NORMAL_DISPLAY;
    return ssd1306_write_command(cmd);
}

ssd1306_status_t ssd1306_display_on_off(bool on) {
    uint8_t cmd = on ? SSD1306_CMD_DISPLAY_ON : SSD1306_CMD_DISPLAY_OFF;
    return ssd1306_write_command(cmd);
}

/*=============================================================================
 * 私有函数实现 Private Function Implementation
 *============================================================================*/

static ssd1306_status_t ssd1306_write_command(uint8_t cmd) {
    uint8_t data[2] = {0x00, cmd}; // 控制字节 + 命令
    
    if (DL_I2C_masterTransmitDataBlocking(SSD1306_I2C_INSTANCE, SSD1306_I2C_ADDRESS, 
                                         data, 2) != DL_I2C_CONTROLLER_STATUS_SUCCESS) {
        return SSD1306_STATUS_I2C_ERROR;
    }
    
    return SSD1306_STATUS_OK;
}

static ssd1306_status_t ssd1306_write_data(const uint8_t *data, uint16_t len) {
    // 分块发送数据 Send data in chunks
    const uint16_t chunk_size = 32;
    uint8_t buffer[chunk_size + 1];
    buffer[0] = 0x40; // 数据控制字节
    
    for (uint16_t i = 0; i < len; i += chunk_size) {
        uint16_t current_chunk = (len - i > chunk_size) ? chunk_size : (len - i);
        memcpy(&buffer[1], &data[i], current_chunk);
        
        if (DL_I2C_masterTransmitDataBlocking(SSD1306_I2C_INSTANCE, SSD1306_I2C_ADDRESS,
                                             buffer, current_chunk + 1) != DL_I2C_CONTROLLER_STATUS_SUCCESS) {
            return SSD1306_STATUS_I2C_ERROR;
        }
    }
    
    return SSD1306_STATUS_OK;
}

static void ssd1306_abs(int16_t *x) {
    if (*x < 0) *x = -*x;
}

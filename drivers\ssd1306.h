/*
 * SSD1306 OLED显示驱动头文件
 * SSD1306 OLED Display Driver Header File
 *
 * 功能：I2C通信、显示控制、图形绘制、中文字符显示
 * Features: I2C communication, display control, graphics drawing, Chinese character display
 */

#ifndef SSD1306_H
#define SSD1306_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "ti_msp_dl_config.h"
#include "config.h"

#ifdef __cplusplus
extern "C" {
#endif

/*=============================================================================
 * SSD1306命令定义 SSD1306 Command Definitions
 *============================================================================*/
#define SSD1306_CMD_DISPLAY_OFF         0xAE    // 关闭显示
#define SSD1306_CMD_DISPLAY_ON          0xAF    // 开启显示
#define SSD1306_CMD_SET_CONTRAST        0x81    // 设置对比度
#define SSD1306_CMD_NORMAL_DISPLAY      0xA6    // 正常显示
#define SSD1306_CMD_INVERSE_DISPLAY     0xA7    // 反色显示
#define SSD1306_CMD_SET_MEMORY_MODE     0x20    // 设置内存模式
#define SSD1306_CMD_SET_COLUMN_ADDR     0x21    // 设置列地址
#define SSD1306_CMD_SET_PAGE_ADDR       0x22    // 设置页地址
#define SSD1306_CMD_SET_START_LINE      0x40    // 设置起始行
#define SSD1306_CMD_SET_SEGMENT_REMAP   0xA1    // 设置段重映射
#define SSD1306_CMD_SET_COM_SCAN_DEC    0xC8    // 设置COM扫描方向
#define SSD1306_CMD_SET_COM_PINS        0xDA    // 设置COM引脚配置
#define SSD1306_CMD_SET_CLOCK_DIV       0xD5    // 设置时钟分频
#define SSD1306_CMD_SET_PRECHARGE       0xD9    // 设置预充电周期
#define SSD1306_CMD_SET_VCOM_DETECT     0xDB    // 设置VCOM检测电平
#define SSD1306_CMD_CHARGE_PUMP         0x8D    // 电荷泵设置

/*=============================================================================
 * 数据结构定义 Data Structure Definitions
 *============================================================================*/

// 显示状态枚举 Display Status Enumeration
typedef enum {
    SSD1306_STATUS_OK = 0,          // 正常 Normal
    SSD1306_STATUS_ERROR,           // 错误 Error
    SSD1306_STATUS_TIMEOUT,         // 超时 Timeout
    SSD1306_STATUS_I2C_ERROR        // I2C错误 I2C error
} ssd1306_status_t;

// 字体大小枚举 Font Size Enumeration
typedef enum {
    SSD1306_FONT_8X16 = 0,          // 8x16 ASCII字体
    SSD1306_FONT_16X16              // 16x16 中文字体
} ssd1306_font_t;

// 显示模式枚举 Display Mode Enumeration
typedef enum {
    SSD1306_MODE_NORMAL = 0,        // 正常模式
    SSD1306_MODE_INVERSE            // 反色模式
} ssd1306_mode_t;

/*=============================================================================
 * 函数声明 Function Declarations
 *============================================================================*/

/**
 * @brief 初始化SSD1306显示器
 * @brief Initialize SSD1306 display
 * @return ssd1306_status_t 初始化状态
 */
ssd1306_status_t ssd1306_init(void);

/**
 * @brief 清空显示缓冲区
 * @brief Clear display buffer
 */
void ssd1306_clear(void);

/**
 * @brief 更新显示内容
 * @brief Update display content
 * @return ssd1306_status_t 更新状态
 */
ssd1306_status_t ssd1306_update(void);

/**
 * @brief 设置像素点
 * @brief Set pixel
 * @param x X坐标 X coordinate
 * @param y Y坐标 Y coordinate
 * @param color 颜色(1=点亮, 0=熄灭) Color (1=on, 0=off)
 */
void ssd1306_set_pixel(uint8_t x, uint8_t y, uint8_t color);

/**
 * @brief 画线
 * @brief Draw line
 * @param x0 起始X坐标 Start X coordinate
 * @param y0 起始Y坐标 Start Y coordinate
 * @param x1 结束X坐标 End X coordinate
 * @param y1 结束Y坐标 End Y coordinate
 * @param color 颜色 Color
 */
void ssd1306_draw_line(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint8_t color);

/**
 * @brief 画矩形
 * @brief Draw rectangle
 * @param x X坐标 X coordinate
 * @param y Y坐标 Y coordinate
 * @param width 宽度 Width
 * @param height 高度 Height
 * @param color 颜色 Color
 */
void ssd1306_draw_rect(uint8_t x, uint8_t y, uint8_t width, uint8_t height, uint8_t color);

/**
 * @brief 显示ASCII字符
 * @brief Display ASCII character
 * @param x X坐标 X coordinate
 * @param y Y坐标 Y coordinate
 * @param ch 字符 Character
 * @param font 字体 Font
 * @param color 颜色 Color
 */
void ssd1306_draw_char(uint8_t x, uint8_t y, char ch, ssd1306_font_t font, uint8_t color);

/**
 * @brief 显示字符串
 * @brief Display string
 * @param x X坐标 X coordinate
 * @param y Y坐标 Y coordinate
 * @param str 字符串 String
 * @param font 字体 Font
 * @param color 颜色 Color
 */
void ssd1306_draw_string(uint8_t x, uint8_t y, const char *str, ssd1306_font_t font, uint8_t color);

/**
 * @brief 显示中文字符
 * @brief Display Chinese character
 * @param x X坐标 X coordinate
 * @param y Y坐标 Y coordinate
 * @param unicode Unicode编码 Unicode code
 * @param color 颜色 Color
 */
void ssd1306_draw_chinese(uint8_t x, uint8_t y, uint16_t unicode, uint8_t color);

/**
 * @brief 显示数字
 * @brief Display number
 * @param x X坐标 X coordinate
 * @param y Y坐标 Y coordinate
 * @param num 数字 Number
 * @param decimal_places 小数位数 Decimal places
 * @param font 字体 Font
 * @param color 颜色 Color
 */
void ssd1306_draw_number(uint8_t x, uint8_t y, float num, uint8_t decimal_places, 
                        ssd1306_font_t font, uint8_t color);

/**
 * @brief 设置对比度
 * @brief Set contrast
 * @param contrast 对比度值(0-255) Contrast value (0-255)
 * @return ssd1306_status_t 设置状态
 */
ssd1306_status_t ssd1306_set_contrast(uint8_t contrast);

/**
 * @brief 设置显示模式
 * @brief Set display mode
 * @param mode 显示模式 Display mode
 * @return ssd1306_status_t 设置状态
 */
ssd1306_status_t ssd1306_set_mode(ssd1306_mode_t mode);

/**
 * @brief 开启/关闭显示
 * @brief Turn display on/off
 * @param on 开启标志 On flag
 * @return ssd1306_status_t 设置状态
 */
ssd1306_status_t ssd1306_display_on_off(bool on);

/*=============================================================================
 * 内联函数 Inline Functions
 *============================================================================*/

/**
 * @brief 获取显示缓冲区指针
 * @brief Get display buffer pointer
 * @return uint8_t* 缓冲区指针 Buffer pointer
 */
static inline uint8_t* ssd1306_get_buffer(void) {
    extern uint8_t ssd1306_buffer[SSD1306_BUFFER_SIZE];
    return ssd1306_buffer;
}

#ifdef __cplusplus
}
#endif

#endif /* SSD1306_H */

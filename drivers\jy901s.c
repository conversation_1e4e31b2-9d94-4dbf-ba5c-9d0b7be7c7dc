/*
 * JY901S九轴姿态传感器驱动实现文件
 * JY901S 9-axis Attitude Sensor Driver Implementation
 */

#include "jy901s.h"

/*=============================================================================
 * 私有变量 Private Variables
 *============================================================================*/
static uint8_t rx_buffer[JY901S_RX_BUFFER_SIZE];   // 接收缓冲区
static volatile uint16_t rx_head = 0;              // 缓冲区头指针
static volatile uint16_t rx_tail = 0;              // 缓冲区尾指针
static jy901s_attitude_t current_attitude = {0};   // 当前姿态数据
static volatile jy901s_status_t sensor_status = JY901S_STATUS_OK;
static volatile uint32_t last_update_time = 0;     // 最后更新时间
static volatile bool data_ready = false;           // 数据就绪标志

/*=============================================================================
 * 私有函数声明 Private Function Declarations
 *============================================================================*/
static bool jy901s_parse_packet(const uint8_t *packet);
static uint16_t jy901s_buffer_available(void);
static uint8_t jy901s_buffer_read(void);
static void jy901s_buffer_write(uint8_t data);

/*=============================================================================
 * 公共函数实现 Public Function Implementation
 *============================================================================*/

jy901s_status_t jy901s_init(void) {
    // 清空缓冲区 Clear buffer
    rx_head = 0;
    rx_tail = 0;
    memset(rx_buffer, 0, sizeof(rx_buffer));
    
    // 初始化姿态数据 Initialize attitude data
    current_attitude.pitch = 0.0f;
    current_attitude.roll = 0.0f;
    current_attitude.yaw = 0.0f;
    current_attitude.valid = false;
    current_attitude.timestamp = 0;
    
    // 重置状态 Reset status
    sensor_status = JY901S_STATUS_OK;
    data_ready = false;
    last_update_time = 0;
    
    // 启用UART接收中断 Enable UART RX interrupt
    DL_UART_enableInterrupt(JY901S_UART_INSTANCE, DL_UART_INTERRUPT_RX);
    
    return JY901S_STATUS_OK;
}

jy901s_status_t jy901s_get_attitude(jy901s_attitude_t *attitude) {
    if (attitude == NULL) {
        return JY901S_STATUS_ERROR;
    }
    
    // 禁用中断保护数据 Disable interrupt to protect data
    DL_UART_disableInterrupt(JY901S_UART_INSTANCE, DL_UART_INTERRUPT_RX);
    
    // 复制当前姿态数据 Copy current attitude data
    *attitude = current_attitude;
    
    // 重新启用中断 Re-enable interrupt
    DL_UART_enableInterrupt(JY901S_UART_INSTANCE, DL_UART_INTERRUPT_RX);
    
    return sensor_status;
}

bool jy901s_is_data_valid(void) {
    return current_attitude.valid && data_ready;
}

uint32_t jy901s_get_last_update_time(void) {
    return last_update_time;
}

void jy901s_uart_irq_handler(void) {
    uint32_t status = DL_UART_getPendingInterrupt(JY901S_UART_INSTANCE);
    
    if (status & DL_UART_IIDX_RX) {
        // 读取接收到的数据 Read received data
        while (DL_UART_isRXFIFOEmpty(JY901S_UART_INSTANCE) == false) {
            uint8_t data = DL_UART_receiveData(JY901S_UART_INSTANCE);
            jy901s_buffer_write(data);
        }
        
        // 处理数据 Process data
        jy901s_process_data();
    }
}

void jy901s_process_data(void) {
    static uint8_t packet_buffer[JY901S_PACKET_SIZE];
    static uint8_t packet_index = 0;
    static bool packet_started = false;
    
    while (jy901s_buffer_available() > 0) {
        uint8_t byte = jy901s_buffer_read();
        
        if (!packet_started) {
            // 寻找包头 Look for packet header
            if (byte == JY901S_FRAME_HEADER) {
                packet_buffer[0] = byte;
                packet_index = 1;
                packet_started = true;
            }
        } else {
            // 收集数据包 Collect packet data
            packet_buffer[packet_index++] = byte;
            
            if (packet_index >= JY901S_PACKET_SIZE) {
                // 数据包接收完成 Packet received completely
                if (jy901s_parse_packet(packet_buffer)) {
                    data_ready = true;
                    last_update_time = DL_Common_getTIMERCount(SYSTEM_TIMER_INSTANCE);
                }
                
                // 重置状态 Reset state
                packet_started = false;
                packet_index = 0;
            }
        }
    }
}

void jy901s_reset(void) {
    sensor_status = JY901S_STATUS_OK;
    current_attitude.valid = false;
    data_ready = false;
    rx_head = 0;
    rx_tail = 0;
}

jy901s_status_t jy901s_get_status(void) {
    return sensor_status;
}

/*=============================================================================
 * 私有函数实现 Private Function Implementation
 *============================================================================*/

static bool jy901s_parse_packet(const uint8_t *packet) {
    if (packet == NULL) return false;
    
    // 检查包头和类型 Check header and type
    if (packet[0] != JY901S_FRAME_HEADER || packet[1] != JY901S_ANGLE_TYPE) {
        return false;
    }
    
    // 计算校验和 Calculate checksum
    uint8_t calculated_checksum = jy901s_calculate_checksum(packet, JY901S_PACKET_SIZE - 1);
    if (calculated_checksum != packet[JY901S_PACKET_SIZE - 1]) {
        sensor_status = JY901S_STATUS_INVALID_DATA;
        return false;
    }
    
    // 解析角度数据 Parse angle data
    int16_t raw_roll = (int16_t)((packet[3] << 8) | packet[2]);
    int16_t raw_pitch = (int16_t)((packet[5] << 8) | packet[4]);
    int16_t raw_yaw = (int16_t)((packet[7] << 8) | packet[6]);
    
    // 转换为角度值 Convert to angle values
    current_attitude.roll = jy901s_raw_to_angle(raw_roll);
    current_attitude.pitch = jy901s_raw_to_angle(raw_pitch);
    current_attitude.yaw = jy901s_raw_to_angle(raw_yaw);
    current_attitude.valid = true;
    current_attitude.timestamp = DL_Common_getTIMERCount(SYSTEM_TIMER_INSTANCE);
    
    sensor_status = JY901S_STATUS_OK;
    return true;
}

static uint16_t jy901s_buffer_available(void) {
    return (rx_head >= rx_tail) ? (rx_head - rx_tail) : 
           (JY901S_RX_BUFFER_SIZE - rx_tail + rx_head);
}

static uint8_t jy901s_buffer_read(void) {
    if (rx_tail == rx_head) return 0; // 缓冲区空 Buffer empty
    
    uint8_t data = rx_buffer[rx_tail];
    rx_tail = (rx_tail + 1) % JY901S_RX_BUFFER_SIZE;
    return data;
}

static void jy901s_buffer_write(uint8_t data) {
    uint16_t next_head = (rx_head + 1) % JY901S_RX_BUFFER_SIZE;
    
    if (next_head != rx_tail) {
        rx_buffer[rx_head] = data;
        rx_head = next_head;
    } else {
        sensor_status = JY901S_STATUS_BUFFER_FULL;
    }
}

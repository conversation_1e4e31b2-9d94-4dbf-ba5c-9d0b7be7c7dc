/*
 * JY901S九轴姿态传感器驱动头文件
 * JY901S 9-axis Attitude Sensor Driver Header File
 *
 * 功能：UART通信、数据解析、姿态角度计算
 * Features: UART communication, data parsing, attitude angle calculation
 */

#ifndef JY901S_H
#define JY901S_H

#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include "ti_msp_dl_config.h"
#include "config.h"

#ifdef __cplusplus
extern "C" {
#endif

/*=============================================================================
 * 数据结构定义 Data Structure Definitions
 *============================================================================*/

// JY901S姿态数据结构 JY901S Attitude Data Structure
typedef struct {
    float pitch;        // 俯仰角 Pitch angle (-180° to +180°)
    float roll;         // 横滚角 Roll angle (-180° to +180°)
    float yaw;          // 偏航角 Yaw angle (-180° to +180°)
    bool valid;         // 数据有效标志 Data valid flag
    uint32_t timestamp; // 时间戳 Timestamp
} jy901s_attitude_t;

// JY901S状态枚举 JY901S Status Enumeration
typedef enum {
    JY901S_STATUS_OK = 0,           // 正常 Normal
    JY901S_STATUS_ERROR,            // 错误 Error
    JY901S_STATUS_TIMEOUT,          // 超时 Timeout
    JY901S_STATUS_INVALID_DATA,     // 无效数据 Invalid data
    JY901S_STATUS_BUFFER_FULL       // 缓冲区满 Buffer full
} jy901s_status_t;

// JY901S数据包结构 JY901S Data Packet Structure
typedef struct {
    uint8_t header;     // 包头 0x55
    uint8_t type;       // 数据类型 0x53(角度)
    uint8_t data[8];    // 数据内容 Data content
    uint8_t checksum;   // 校验和 Checksum
} __attribute__((packed)) jy901s_packet_t;

/*=============================================================================
 * 函数声明 Function Declarations
 *============================================================================*/

/**
 * @brief 初始化JY901S传感器
 * @brief Initialize JY901S sensor
 * @return jy901s_status_t 初始化状态
 */
jy901s_status_t jy901s_init(void);

/**
 * @brief 获取姿态数据
 * @brief Get attitude data
 * @param attitude 姿态数据指针 Pointer to attitude data
 * @return jy901s_status_t 获取状态
 */
jy901s_status_t jy901s_get_attitude(jy901s_attitude_t *attitude);

/**
 * @brief 检查数据是否有效
 * @brief Check if data is valid
 * @return bool 数据有效性 Data validity
 */
bool jy901s_is_data_valid(void);

/**
 * @brief 获取最后更新时间
 * @brief Get last update time
 * @return uint32_t 时间戳 Timestamp
 */
uint32_t jy901s_get_last_update_time(void);

/**
 * @brief UART接收中断处理函数
 * @brief UART receive interrupt handler
 */
void jy901s_uart_irq_handler(void);

/**
 * @brief 处理接收到的数据
 * @brief Process received data
 */
void jy901s_process_data(void);

/**
 * @brief 重置传感器状态
 * @brief Reset sensor status
 */
void jy901s_reset(void);

/**
 * @brief 获取传感器状态
 * @brief Get sensor status
 * @return jy901s_status_t 传感器状态
 */
jy901s_status_t jy901s_get_status(void);

/*=============================================================================
 * 内联函数 Inline Functions
 *============================================================================*/

/**
 * @brief 计算校验和
 * @brief Calculate checksum
 * @param data 数据指针 Data pointer
 * @param len 数据长度 Data length
 * @return uint8_t 校验和 Checksum
 */
static inline uint8_t jy901s_calculate_checksum(const uint8_t *data, uint8_t len) {
    uint8_t sum = 0;
    for (uint8_t i = 0; i < len; i++) {
        sum += data[i];
    }
    return sum;
}

/**
 * @brief 将16位数据转换为角度
 * @brief Convert 16-bit data to angle
 * @param raw_data 原始数据 Raw data
 * @return float 角度值 Angle value
 */
static inline float jy901s_raw_to_angle(int16_t raw_data) {
    return (float)raw_data / JY901S_ANGLE_SCALE * JY901S_ANGLE_RANGE;
}

#ifdef __cplusplus
}
#endif

#endif /* JY901S_H */

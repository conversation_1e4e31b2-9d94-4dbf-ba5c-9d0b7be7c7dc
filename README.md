# MSPM0G3507 JY901S姿态传感器显示系统

## 项目概述 Project Overview

基于MSPM0G3507开发板的JY901S九轴姿态传感器数据采集与OLED显示系统。实时显示俯仰角、横滚角、偏航角等姿态信息，支持中文界面显示。

JY901S 9-axis attitude sensor data acquisition and OLED display system based on MSPM0G3507 development board. Real-time display of pitch, roll, yaw and other attitude information with Chinese interface support.

## 功能特性 Features

- ✅ **JY901S传感器支持**：UART通信，9600波特率，自动数据解析
- ✅ **OLED显示**：128x64 SSD1306显示屏，I2C通信
- ✅ **中文界面**：支持中文标签显示，用户友好
- ✅ **实时更新**：10Hz数据刷新，流畅显示
- ✅ **状态指示**：LED指示连接状态和系统错误
- ✅ **错误处理**：完善的错误检测和恢复机制
- ✅ **低功耗设计**：智能刷新，仅在数据变化时更新显示

## 硬件连接 Hardware Connections

### JY901S传感器连接 JY901S Sensor Connection

| JY901S引脚 | MSPM0G3507引脚 | 功能 Function |
|------------|----------------|---------------|
| VCC        | 3.3V           | 电源 Power    |
| GND        | GND            | 地线 Ground   |
| TX         | PA11 (UART0_RX)| 数据发送 Data TX |
| RX         | PA10 (UART0_TX)| 数据接收 Data RX |

### SSD1306 OLED显示屏连接 SSD1306 OLED Display Connection

| OLED引脚   | MSPM0G3507引脚 | 功能 Function |
|------------|----------------|---------------|
| VCC        | 3.3V           | 电源 Power    |
| GND        | GND            | 地线 Ground   |
| SCL        | PA0 (I2C0_SCL) | 时钟线 Clock  |
| SDA        | PA1 (I2C0_SDA) | 数据线 Data   |

### 状态LED Status LED

| 功能 Function | 引脚 Pin | 状态 Status |
|---------------|----------|-------------|
| 状态指示 Status | PA26 | 常亮=正常，快闪=错误，慢闪=无数据 |

## 外设配置 Peripheral Configuration

| 外设 Peripheral | 引脚 Pin | 功能 Function | 配置 Configuration |
|-----------------|----------|---------------|-------------------|
| UART0 | PA10/PA11 | JY901S通信 | 9600波特率，8N1，RX中断 |
| I2C0 | PA0/PA1 | OLED显示 | 100kHz，主机模式 |
| GPIO | PA26 | 状态LED | 输出模式 |
| TIMER | TIMG0 | 系统定时 | 100ms周期中断 |
| DEBUGSS | PA19/PA20 | 调试接口 | SWD调试 |

## 软件架构 Software Architecture

```
├── config.h              # 统一配置管理
├── drivers/               # 驱动层
│   ├── jy901s.c/h        # JY901S传感器驱动
│   ├── ssd1306.c/h       # SSD1306显示驱动
│   └── font.c/h          # 字体数据
├── app/                   # 应用层
│   └── attitude_display.c/h  # 姿态显示逻辑
├── empty.c               # 主程序
├── empty.syscfg          # SysConfig配置
└── ti_msp_dl_config.c/h  # 硬件配置
```

## 编译和使用 Build and Usage

### 开发环境要求 Development Environment

- **IDE**: Code Composer Studio (CCS) 12.0+
- **SDK**: MSPM0 SDK 2.04.00.06+
- **工具链**: TI ARM Clang或GCC ARM

### 编译步骤 Build Steps

1. **导入项目 Import Project**
   ```bash
   # 在CCS中导入项目
   File → Import → Code Composer Studio → CCS Projects
   ```

2. **配置SysConfig Configure SysConfig**
   ```bash
   # 双击empty.syscfg文件进行配置
   # 确认UART0和I2C0配置正确
   ```

3. **编译项目 Build Project**
   ```bash
   # 右键项目 → Build Project
   # 或使用快捷键 Ctrl+B
   ```

4. **下载和运行 Download and Run**
   ```bash
   # 连接LaunchPad到PC
   # 右键项目 → Debug As → Code Composer Studio Debug
   ```

### 使用说明 Usage Instructions

1. **硬件连接**：按照上述连接表连接JY901S和OLED显示屏
2. **上电启动**：系统自动初始化，显示"姿态传感器"标题
3. **数据显示**：实时显示三个角度值：
   - 俯仰角：-180° ~ +180°
   - 横滚角：-180° ~ +180°
   - 偏航角：-180° ~ +180°
4. **状态指示**：
   - LED常亮：系统正常工作
   - LED慢闪：传感器无数据
   - LED快闪：系统错误

## 配置参数 Configuration Parameters

主要配置参数在 `config.h` 文件中定义：

```c
// JY901S配置
#define JY901S_UART_BAUDRATE        9600        // 波特率
#define JY901S_RX_BUFFER_SIZE       128         // 接收缓冲区大小

// OLED配置
#define SSD1306_I2C_ADDRESS         0x3C        // I2C地址
#define SSD1306_REFRESH_RATE        10          // 刷新频率(Hz)

// 显示配置
#define DISPLAY_DECIMAL_PLACES      1           // 小数位数
#define DISPLAY_UPDATE_INTERVAL     100         // 更新间隔(ms)
```

## 故障排除 Troubleshooting

### 常见问题 Common Issues

1. **OLED无显示**
   - 检查I2C连接线
   - 确认I2C地址(0x3C或0x3D)
   - 检查电源供电

2. **传感器无数据**
   - 检查UART连接线
   - 确认波特率设置(9600)
   - 检查JY901S电源

3. **编译错误**
   - 确认SDK版本兼容性
   - 检查头文件路径
   - 重新生成SysConfig文件

### 调试方法 Debug Methods

1. **LED状态指示**：观察PA26 LED的闪烁模式
2. **串口调试**：可添加UART1作为调试输出
3. **CCS调试器**：使用断点和变量监视

## 性能参数 Performance Parameters

- **数据更新频率**：10Hz
- **显示刷新延迟**：<100ms
- **内存使用**：<8KB RAM，<32KB Flash
- **功耗**：<50mA @3.3V（正常工作）

## 扩展功能 Extended Features

系统支持以下扩展：

1. **数据记录**：添加SD卡存储历史数据
2. **无线传输**：集成WiFi/蓝牙模块
3. **多传感器**：支持多个JY901S传感器
4. **报警功能**：角度超限报警
5. **校准功能**：传感器零点校准

## 技术支持 Technical Support

- **文档**：参考MSPM0 SDK用户指南
- **示例**：查看SDK中的UART和I2C示例
- **社区**：TI E2E论坛MSPM0版块

## 版本历史 Version History

- **v1.0.0** (2024-12-29)
  - 初始版本发布
  - 支持JY901S数据采集
  - 支持OLED中文显示
  - 完整的错误处理机制

## 许可证 License

本项目基于TI BSD许可证开源。详见项目文件中的许可证声明。

This project is open source under TI BSD License. See license statements in project files for details.

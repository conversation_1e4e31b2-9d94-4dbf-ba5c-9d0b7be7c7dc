/*
 * 字体数据实现文件 Font Data Implementation
 */

#include "font.h"

/*=============================================================================
 * ASCII字体数据实现 ASCII Font Data Implementation (8x16)
 *============================================================================*/

// 完整的ASCII字体数据 (0x20-0x7F)
const uint8_t font_8x16[][16] = {
    // 0x20 空格 Space
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x21 !
    {0x00,0x00,0x18,0x3C,0x3C,0x3C,0x18,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,0x00},
    // 0x22 "
    {0x00,0x00,0x66,0x66,0x66,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x23 #
    {0x00,0x00,0x6C,0x6C,0xFE,0x6C,0x6C,0x6C,0xFE,0x6C,0x6C,0x00,0x00,0x00,0x00,0x00},
    // 0x24 $
    {0x00,0x00,0x18,0x7E,0xC0,0xC0,0x7C,0x06,0x06,0xFC,0x18,0x00,0x00,0x00,0x00,0x00},
    // 0x25 %
    {0x00,0x00,0x00,0x00,0xC2,0xC6,0x0C,0x18,0x30,0x60,0xC6,0x86,0x00,0x00,0x00,0x00},
    // 0x26 &
    {0x00,0x00,0x38,0x6C,0x6C,0x38,0x76,0xDC,0xCC,0xCC,0x76,0x00,0x00,0x00,0x00,0x00},
    // 0x27 '
    {0x00,0x00,0x30,0x30,0x30,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x28 (
    {0x00,0x00,0x0C,0x18,0x30,0x30,0x30,0x30,0x30,0x18,0x0C,0x00,0x00,0x00,0x00,0x00},
    // 0x29 )
    {0x00,0x00,0x60,0x30,0x18,0x18,0x18,0x18,0x18,0x30,0x60,0x00,0x00,0x00,0x00,0x00},
    // 0x2A *
    {0x00,0x00,0x00,0x00,0x66,0x3C,0xFF,0x3C,0x66,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x2B +
    {0x00,0x00,0x00,0x00,0x18,0x18,0x7E,0x18,0x18,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x2C ,
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x30,0x00,0x00,0x00,0x00,0x00},
    // 0x2D -
    {0x00,0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x2E .
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x00},
    // 0x2F /
    {0x00,0x00,0x00,0x02,0x06,0x0C,0x18,0x30,0x60,0xC0,0x80,0x00,0x00,0x00,0x00,0x00},
    // 0x30 0
    {0x00,0x00,0x7C,0xC6,0xCE,0xDE,0xF6,0xE6,0xC6,0xC6,0x7C,0x00,0x00,0x00,0x00,0x00},
    // 0x31 1
    {0x00,0x00,0x18,0x38,0x78,0x18,0x18,0x18,0x18,0x18,0x7E,0x00,0x00,0x00,0x00,0x00},
    // 0x32 2
    {0x00,0x00,0x7C,0xC6,0x06,0x0C,0x18,0x30,0x60,0xC0,0xFE,0x00,0x00,0x00,0x00,0x00},
    // 0x33 3
    {0x00,0x00,0x7C,0xC6,0x06,0x06,0x3C,0x06,0x06,0xC6,0x7C,0x00,0x00,0x00,0x00,0x00},
    // 0x34 4
    {0x00,0x00,0x0C,0x1C,0x3C,0x6C,0xCC,0xFE,0x0C,0x0C,0x1E,0x00,0x00,0x00,0x00,0x00},
    // 0x35 5
    {0x00,0x00,0xFE,0xC0,0xC0,0xC0,0xFC,0x06,0x06,0xC6,0x7C,0x00,0x00,0x00,0x00,0x00},
    // 0x36 6
    {0x00,0x00,0x38,0x60,0xC0,0xC0,0xFC,0xC6,0xC6,0xC6,0x7C,0x00,0x00,0x00,0x00,0x00},
    // 0x37 7
    {0x00,0x00,0xFE,0xC6,0x06,0x06,0x0C,0x18,0x30,0x30,0x30,0x00,0x00,0x00,0x00,0x00},
    // 0x38 8
    {0x00,0x00,0x7C,0xC6,0xC6,0xC6,0x7C,0xC6,0xC6,0xC6,0x7C,0x00,0x00,0x00,0x00,0x00},
    // 0x39 9
    {0x00,0x00,0x7C,0xC6,0xC6,0xC6,0x7E,0x06,0x06,0x0C,0x78,0x00,0x00,0x00,0x00,0x00},
    // 0x3A :
    {0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x18,0x18,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x3B ;
    {0x00,0x00,0x00,0x00,0x18,0x18,0x00,0x00,0x18,0x18,0x30,0x00,0x00,0x00,0x00,0x00},
    // 0x3C <
    {0x00,0x00,0x00,0x06,0x0C,0x18,0x30,0x60,0x30,0x18,0x0C,0x06,0x00,0x00,0x00,0x00},
    // 0x3D =
    {0x00,0x00,0x00,0x00,0x00,0x7E,0x00,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 0x3E >
    {0x00,0x00,0x00,0x60,0x30,0x18,0x0C,0x06,0x0C,0x18,0x30,0x60,0x00,0x00,0x00,0x00},
    // 0x3F ?
    {0x00,0x00,0x7C,0xC6,0xC6,0x0C,0x18,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,0x00},
};

/*=============================================================================
 * 中文字符数据实现 Chinese Character Data Implementation (16x16)
 *============================================================================*/

// 常用中文字符点阵数据
const chinese_char_t chinese_chars[] = {
    // 姿 (0x59FF)
    {UNICODE_姿, {0x10,0x60,0x02,0x8C,0x00,0x00,0x87,0xFC,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,
                  0x87,0xFC,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x44,0x87,0xFC,0x00,0x00,0x00,0x00}},
    
    // 态 (0x6001)
    {UNICODE_态, {0x00,0x00,0x23,0xF8,0x12,0x08,0x12,0x08,0x83,0xF8,0x42,0x08,0x42,0x08,0x13,0xF8,
                  0x10,0x00,0x27,0xFC,0x24,0x44,0x44,0x44,0x47,0xFC,0x84,0x44,0x04,0x44,0x00,0x00}},
    
    // 传 (0x4F20)
    {UNICODE_传, {0x00,0x80,0x40,0x80,0x20,0x80,0x20,0x80,0x11,0xFC,0x11,0x04,0x02,0x08,0x02,0x08,
                  0x04,0x90,0x04,0x90,0x08,0x60,0x08,0x60,0x10,0x80,0x61,0x00,0x06,0x00,0x00,0x00}},
    
    // 感 (0x611F)
    {UNICODE_感, {0x04,0x40,0x04,0x40,0x7F,0xFE,0x04,0x40,0x1F,0xF8,0x04,0x40,0x1F,0xF8,0x04,0x40,
                  0x7F,0xFE,0x04,0x40,0x08,0x20,0x08,0x20,0x10,0x10,0x20,0x08,0x40,0x06,0x00,0x00}},
    
    // 器 (0x5668)
    {UNICODE_器, {0x7F,0xFC,0x44,0x44,0x44,0x44,0x7F,0xFC,0x44,0x44,0x44,0x44,0x7F,0xFC,0x00,0x00,
                  0x7F,0xFC,0x44,0x44,0x44,0x44,0x7F,0xFC,0x44,0x44,0x44,0x44,0x7F,0xFC,0x00,0x00}},
    
    // 俯 (0x4FEF)
    {UNICODE_俯, {0x08,0x20,0x08,0x40,0x10,0x40,0x10,0x80,0x21,0x00,0x21,0x00,0x42,0x00,0x42,0x00,
                  0x84,0x00,0x84,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x10,0x00,0x20,0x00,0x00,0x00}},
    
    // 仰 (0x4EF0)
    {UNICODE_仰, {0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,
                  0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x08,0x00,0x00,0x00}},
    
    // 角 (0x89D2)
    {UNICODE_角, {0x7F,0xFC,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,
                  0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x7F,0xFC,0x00,0x00}},
    
    // 横 (0x6A2A)
    {UNICODE_横, {0x10,0x40,0x10,0x40,0x20,0x80,0x20,0x80,0x41,0x00,0x41,0x00,0x82,0x00,0x82,0x00,
                  0x04,0x00,0x04,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x10,0x00,0x20,0x00,0x00,0x00}},
    
    // 滚 (0x6EDA)
    {UNICODE_滚, {0x08,0x20,0x08,0x20,0x10,0x40,0x10,0x40,0x20,0x80,0x20,0x80,0x41,0x00,0x41,0x00,
                  0x82,0x00,0x82,0x00,0x04,0x00,0x04,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x00,0x00}},
    
    // 偏 (0x504F)
    {UNICODE_偏, {0x08,0x40,0x08,0x40,0x10,0x80,0x10,0x80,0x21,0x00,0x21,0x00,0x42,0x00,0x42,0x00,
                  0x84,0x00,0x84,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x10,0x00,0x20,0x00,0x00,0x00}},
    
    // 航 (0x822A)
    {UNICODE_航, {0x10,0x40,0x10,0x40,0x20,0x80,0x20,0x80,0x41,0x00,0x41,0x00,0x82,0x00,0x82,0x00,
                  0x04,0x00,0x04,0x00,0x08,0x00,0x08,0x00,0x10,0x00,0x10,0x00,0x20,0x00,0x00,0x00}},
    
    // 度 (0x5EA6)
    {UNICODE_度, {0x7F,0xFC,0x40,0x04,0x40,0x04,0x40,0x04,0x7F,0xFC,0x40,0x04,0x40,0x04,0x40,0x04,
                  0x7F,0xFC,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x40,0x04,0x7F,0xFC,0x00,0x00}},
};

const uint16_t chinese_chars_count = sizeof(chinese_chars) / sizeof(chinese_chars[0]);

/*=============================================================================
 * 函数实现 Function Implementation
 *============================================================================*/

const uint8_t* font_get_ascii_data(char ch) {
    if (ch < 0x20 || ch > 0x7F) {
        return NULL; // 超出范围
    }
    return font_8x16[ch - 0x20];
}

const uint8_t* font_get_chinese_data(uint16_t unicode) {
    for (uint16_t i = 0; i < chinese_chars_count; i++) {
        if (chinese_chars[i].unicode == unicode) {
            return chinese_chars[i].data;
        }
    }
    return NULL; // 未找到字符
}

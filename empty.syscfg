/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 */
//@cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default"
//@v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)"
// @cliArgs --board /ti/boards/LP_MSPM0G3507 --rtos nortos

/**
 * Import the modules used in this configuration.
 */
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const UART0 = scripting.addModule("/ti/driverlib/UART", {}, false);
const I2C0 = scripting.addModule("/ti/driverlib/I2C", {}, false);
const GPIO = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const TIMER = scripting.addModule("/ti/driverlib/TIMER", {}, false);

/**
 * Write custom configuration values to the imported modules.
 */

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

// 系统时钟配置 System Clock Configuration
SYSCTL.forceDefaultClkConfig = true;

// UART0配置用于JY901S传感器 UART0 Configuration for JY901S Sensor
UART0.$name = "JY901S_UART";
UART0.profile = "CONFIG_PROFILE_1";
UART0.targetBaudRate = 9600;
UART0.enabledInterrupts = ["RX"];
UART0.rxFifoThreshold = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART0.peripheral.$assign = "UART0";
UART0.peripheral.rxPin.$assign = "PA11";
UART0.peripheral.txPin.$assign = "PA10";

// I2C0配置用于SSD1306 OLED I2C0 Configuration for SSD1306 OLED
I2C0.$name = "SSD1306_I2C";
I2C0.basicEnableController = true;
I2C0.basicControllerBusSpeed = 100000;
I2C0.basicControllerStandardBusTimeout = 255;
I2C0.peripheral.$assign = "I2C0";
I2C0.peripheral.sdaPin.$assign = "PA1";
I2C0.peripheral.sclPin.$assign = "PA0";

// GPIO配置用于状态LED GPIO Configuration for Status LED
GPIO.$name = "STATUS_GPIO";
const gpio1 = GPIO.addInstance();
gpio1.$name = "STATUS_LED";
gpio1.port = "PORTA";
gpio1.portSegment = "Upper";
gpio1.pin = 26;
gpio1.direction = "OUTPUT";
gpio1.initialValue = "SET";

// 定时器配置用于系统定时 Timer Configuration for System Timing
TIMER.$name = "SYSTEM_TIMER";
TIMER.timerMode = "PERIODIC";
TIMER.timerPeriod = "100 ms";
TIMER.enabledInterrupts = ["ZERO"];
TIMER.peripheral.$assign = "TIMG0";

/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 */
//@cliArgs --device "MSPM0G350X" --package "LQFP-64(PM)" --part "Default"
//@v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)"
// @cliArgs --board /ti/boards/LP_MSPM0G3507 --rtos nortos

/**
 * Import the modules used in this configuration.
 */
const SYSCTL = scripting.addModule("/ti/driverlib/SYSCTL");
const UART = scripting.addModule("/ti/driverlib/UART", {}, false);
const I2C = scripting.addModule("/ti/driverlib/I2C", {}, false);
const GPIO = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const TIMER = scripting.addModule("/ti/driverlib/TIMER", {}, false);

/**
 * Write custom configuration values to the imported modules.
 */

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

// 系统时钟配置 System Clock Configuration
SYSCTL.forceDefaultClkConfig = true;

// UART配置用于JY901S传感器 UART Configuration for JY901S Sensor
UART.profile = "CONFIG_PROFILE_1";
UART.targetBaudRate = 9600;
UART.enabledInterrupts = ["RX"];
UART.rxFifoThreshold = "DL_UART_RX_FIFO_LEVEL_ONE_ENTRY";
UART.peripheral.$assign = "UART0";
UART.peripheral.rxPin.$assign = "PA11";
UART.peripheral.txPin.$assign = "PA10";

// I2C配置用于SSD1306 OLED I2C Configuration for SSD1306 OLED
I2C.basicEnableController = true;
I2C.basicControllerBusSpeed = 100000;
I2C.basicControllerStandardBusTimeout = 255;
I2C.peripheral.$assign = "I2C0";
I2C.peripheral.sdaPin.$assign = "PA1";
I2C.peripheral.sclPin.$assign = "PA0";

// GPIO配置用于状态LED GPIO Configuration for Status LED
const gpio1 = GPIO.addInstance();
gpio1.port = "PORTA";
gpio1.portSegment = "Upper";
gpio1.pin = 26;
gpio1.direction = "OUTPUT";
gpio1.initialValue = "SET";

// 定时器配置用于系统定时 Timer Configuration for System Timing
TIMER.timerMode = "PERIODIC";
TIMER.timerPeriod = "100 ms";
TIMER.enabledInterrupts = ["ZERO"];
TIMER.peripheral.$assign = "TIMG0";

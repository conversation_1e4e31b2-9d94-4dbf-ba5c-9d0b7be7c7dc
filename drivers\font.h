/*
 * 字体数据文件 Font Data File
 * 包含ASCII字符和中文字符点阵数据
 * Contains ASCII and Chinese character bitmap data
 */

#ifndef FONT_H
#define FONT_H

#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

/*=============================================================================
 * ASCII字体数据 ASCII Font Data (8x16)
 *============================================================================*/
// 8x16 ASCII字体数据 (简化版，包含数字、字母、符号)
const uint8_t font_8x16[][16] = {
    // 空格 (0x20)
    {0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // ! (0x21)
    {0x00,0x00,0x18,0x3C,0x3C,0x3C,0x18,0x18,0x18,0x00,0x18,0x18,0x00,0x00,0x00,0x00},
    // " (0x22)
    {0x00,0x00,0x66,0x66,0x66,0x24,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},
    // 数字0-9的字体数据将在实现文件中定义
};

/*=============================================================================
 * 中文字符数据 Chinese Character Data (16x16)
 *============================================================================*/

// 中文字符结构 Chinese Character Structure
typedef struct {
    uint16_t unicode;       // Unicode编码
    uint8_t data[32];       // 16x16点阵数据 (32字节)
} chinese_char_t;

// 常用中文字符数据 Common Chinese Characters Data
extern const chinese_char_t chinese_chars[];
extern const uint16_t chinese_chars_count;

/*=============================================================================
 * 中文字符Unicode定义 Chinese Character Unicode Definitions
 *============================================================================*/
#define UNICODE_姿      0x59FF  // 姿
#define UNICODE_态      0x6001  // 态
#define UNICODE_传      0x4F20  // 传
#define UNICODE_感      0x611F  // 感
#define UNICODE_器      0x5668  // 器
#define UNICODE_俯      0x4FEF  // 俯
#define UNICODE_仰      0x4EF0  // 仰
#define UNICODE_角      0x89D2  // 角
#define UNICODE_横      0x6A2A  // 横
#define UNICODE_滚      0x6EDA  // 滚
#define UNICODE_偏      0x504F  // 偏
#define UNICODE_航      0x822A  // 航
#define UNICODE_连      0x8FDE  // 连
#define UNICODE_接      0x63A5  // 接
#define UNICODE_中      0x4E2D  // 中
#define UNICODE_错      0x9519  // 错
#define UNICODE_误      0x8BEF  // 误
#define UNICODE_度      0x5EA6  // 度

/*=============================================================================
 * 函数声明 Function Declarations
 *============================================================================*/

/**
 * @brief 获取ASCII字符点阵数据
 * @brief Get ASCII character bitmap data
 * @param ch 字符 Character
 * @return const uint8_t* 点阵数据指针 Bitmap data pointer
 */
const uint8_t* font_get_ascii_data(char ch);

/**
 * @brief 获取中文字符点阵数据
 * @brief Get Chinese character bitmap data
 * @param unicode Unicode编码 Unicode code
 * @return const uint8_t* 点阵数据指针 Bitmap data pointer
 */
const uint8_t* font_get_chinese_data(uint16_t unicode);

#ifdef __cplusplus
}
#endif

#endif /* FONT_H */
